"use client";

import Image from "next/image";
import { useState } from "react";
import { useRouter } from "next/navigation";
import BackgroundLayout from "../components/BackgroundLayout";

export default function AgeVerification() {
  const [selectedAge, setSelectedAge] = useState<"over18" | "under18" | null>(null);
  const router = useRouter();

  const handleAgeSelection = (age: "over18" | "under18") => {
    setSelectedAge(age);
    // Handle navigation logic here
    if (age === "over18") {
      // Navigate to parent/guardian approval page
      router.push("/parent-guardian-approval");
    } else {
      // Handle under 18 case
      console.log("User is under 18");
    }
  };

  return (
    <BackgroundLayout>
      {/* Header */}
      <header className="flex justify-between items-center p-8">
        <div className="flex items-center">
          <Image src="/images/koach_white.png" alt="Koach" width={140} height={60} className="mr-3" />
        </div>
        <button className="text-white text-lg font-medium hover:text-white/80 transition-colors">Terms and Conditions</button>
      </header>

      {/* Main Content */}
      <main className="flex justify-center items-center min-h-[calc(100vh-200px)] px-4">
        <div className="bg-[#eefff5]/95 backdrop-blur-sm rounded-3xl p-12 max-w-4xl w-full mx-4 shadow-2xl border border-green-400">
          {/* Title */}
          <div className="text-center mb-12">
            <h1 className="text-4xl md:text-5xl font-bold text-slate-700 mb-6">Age Verification</h1>
            <p className="text-slate-600 text-lg">Before proceeding to the Terms and Conditions, please confirm your age</p>
          </div>

          {/* Age Selection Cards */}
          <div className="grid md:grid-cols-2 gap-6 max-w-3xl mx-auto">
            {/* Over 18 Card */}
            <div className="bg-white rounded-3xl p-8 border border-green-400 hover:border-green-300 transition-all duration-300 hover:shadow-lg text-center shadow-sm flex flex-row md:flex-col justify-around  ">
              <div className="mb-6 flex justify-center">
                <Image src="/images/over-18.png" alt="Over 18" width={250} height={250} className="object-contain" />
              </div>
              <div>
                <h2 className="font-base md:text-3xl font-bold text-slate-700 mb-8">Over 18</h2>
                <button
                  onClick={() => handleAgeSelection("over18")}
                  className="w-full bg-green-500 hover:bg-transparent border hover:border-green-400 text-white hover:text-green-500 font-bold py-1 md:py-4 px-4 md:px-8  rounded-2xl transition-colors text-base md:text-lg cursor-pointer"
                >
                  Select
                </button>
              </div>
            </div>

            {/* Under 18 Card */}
            <div className="bg-white rounded-3xl p-8 border border-green-400 hover:border-green-300 transition-all duration-300 hover:shadow-lg text-center shadow-sm flex flex-row md:flex-col justify-end  ">
              <div className="mb-6 flex justify-center">
                <Image src="/images/under-18.png" alt="Under 18" width={250} height={250} className="object-contain" />
              </div>
              <div>
                <h2 className="font-base md:text-3xl font-bold text-slate-700 mb-8">Below 18</h2>
                <button
                  onClick={() => handleAgeSelection("under18")}
                  className="w-full bg-green-500 hover:bg-transparent border hover:border-green-400 border-green-500 text-white hover:text-green-500 font-bold py-1 md:py-4 px-4 md:px-8 rounded-2xl transition-colors text-base md:text-lg cursor-pointer"
                >
                  Select
                </button>
              </div>
            </div>
          </div>
        </div>
      </main>
    </BackgroundLayout>
  );
}
