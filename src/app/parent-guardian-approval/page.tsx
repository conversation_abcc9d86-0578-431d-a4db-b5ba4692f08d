"use client";

import Image from "next/image";
import { useState } from "react";
import { useRouter } from "next/navigation";

export default function ParentGuardianApproval() {
  const [email, setEmail] = useState("");

  const router = useRouter();

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // Handle email submission logic here
    console.log("Submitting email:", email);
    // Navigate to email-sent page
    router.push("/email-sent");
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="flex justify-end items-center p-8 bg-white shadow-sm">
        <button className="text-slate-600 text-lg font-medium hover:text-slate-800 transition-colors">Terms and Conditions</button>
      </header>

      {/* Main Content */}
      <main className="max-w-6xl mx-auto px-4 py-12">
        <div className="flex flex-col lg:flex-row gap-12 items-start">
          {/* Left Side - Content */}
          <div className="flex-1 space-y-8">
            {/* Logo */}
            <div className="flex justify-center">
              <Image src="/images/koach-logo-2.png" alt="Koach" width={50} height={60}/>
            </div>

            {/* Title */}
            <div className="flex justify-center">
              <h1 className="text-1xl md:text-3xl font-bold text-slate-800">Parent or guardian approval required</h1>
            </div>

            {/* Info Box */}
            <div className="border border-blue-200 rounded-lg p-4 bg-blue-50">
              <p className="text-blue-800">As you are under 18, we require a parent or guardian to accept the Terms and Conditions on your behalf.</p>
            </div>

            {/* Instructions */}
            <p className="text-slate-600">
              Please provide a valid email address for your parent or guardian so they can complete this process for you. An email will be sent to them on your
              behalf.
            </p>

            {/* Email Form */}
            <form onSubmit={handleSubmit} className="space-y-6">
              <label htmlFor="parentEmail" className="block text-slate-800 font-semibold mb-2">
                Parent/Guardian Email Address
              </label>
              <div className="flex flex-col md:flex-row justify-between gap-4">
                <input
                  type="email"
                  id="parentEmail"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  placeholder="<EMAIL>"
                  className="w-full px-1 py-1 border border-gray-300 rounded-4xl focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 text-black"
                  required
                />
                <button type="submit" className="bg-green-500 hover:bg-green-600 text-white font-medium py-1 px-3  rounded-md transition-colors">
                  Submit
                </button>
              </div>
            </form>
          </div>

          {/* Right Side - Illustration */}
          <div className="flex-1 flex justify-center">
            <Image src="/images/parent-guardian-approval.png" alt="Family illustration" width={500} height={400} className="object-contain" />
          </div>
        </div>
      </main>
    </div>
  );
}
