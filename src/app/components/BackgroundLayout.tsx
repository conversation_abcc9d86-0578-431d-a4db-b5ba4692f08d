interface BackgroundLayoutProps {
  children: React.ReactNode;
  className?: string;
  topHeight?: string;
  bottomHeight?: string;
  topImage?: string;
  bottomImage?: string;
}

export default function BackgroundLayout({
  children,
  className = "",
  topHeight = "30%",
  bottomHeight = "50%",
  topImage = "/images/top-bg.png",
  bottomImage = "/images/bottom-bg.png",
}: BackgroundLayoutProps) {
  return (
    <div className={`min-h-screen relative overflow-hidden bg-white ${className}`}>
      {/* Top curved section */}
      <div
        className={`absolute top-0 left-0 w-full z-0`}
        style={{
          height: topHeight,
          backgroundImage: `url('${topImage}')`,
          backgroundPosition: "top center",
          backgroundRepeat: "no-repeat",
          backgroundSize: "cover",
        }}
      ></div>

      {/* Bottom curved section */}
      <div
        className={`absolute bottom-0 left-0 w-full z-0`}
        style={{
          height: bottomHeight,
          backgroundImage: `url('${bottomImage}')`,
          backgroundPosition: "bottom center",
          backgroundRepeat: "no-repeat",
          backgroundSize: "cover",
        }}
      ></div>

      {/* Content */}
      <div className="relative z-10">{children}</div>
    </div>
  );
}
