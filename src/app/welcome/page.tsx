"use client";

import Image from "next/image";
import { useRouter } from "next/navigation";
import BackgroundLayout from "../components/BackgroundLayout";

export default function Welcome() {
  const router = useRouter();

  const handleContinue = () => {
    // Navigate to age verification page
    router.push("/age-verification");
  };

  return (
    <BackgroundLayout>
      {/* Header */}
      <header className="flex justify-between items-center p-8">
        <div className="flex items-center">
          <Image src="/images/koach_white.png" alt="Koach" width={140} height={60} className="mr-3" />
        </div>
        <button className="text-white text-lg font-medium hover:text-white/80 transition-colors">
          Terms and Conditions
        </button>
      </header>

      {/* Main Content */}
      <main className="flex justify-center items-center min-h-[calc(100vh-200px)] px-4">
        <div className="grid lg:grid-cols-2 gap-8 max-w-7xl w-full mx-4">
          {/* Left Content */}
          <div className="flex flex-col justify-center">
            {/* Koach Logo */}
            <div className="flex items-center mb-8">
              <div className="bg-white rounded-2xl p-4 mr-4 shadow-lg">
                <Image src="/images/koach-logo.png" alt="Koach" width={60} height={60} />
              </div>
              <h2 className="text-3xl font-bold text-slate-700">Koach</h2>
            </div>

            {/* Welcome Title */}
            <h1 className="text-4xl md:text-5xl font-bold text-slate-700 mb-6">
              Welcome to Koach Hub
            </h1>

            {/* Player Invitation */}
            <p className="text-xl text-green-600 font-semibold mb-8">
              [Player Name] has been invited to join [Club Name]
            </p>

            {/* Information Card */}
            <div className="bg-white/95 backdrop-blur-sm rounded-2xl p-8 shadow-lg border border-green-200 mb-8">
              <h3 className="text-lg font-semibold text-slate-700 mb-4">
                Before you get started:
              </h3>
              <p className="text-slate-600 mb-4">
                To complete the registration and receive the login credentials, please 
                review and accept our Terms and Conditions.
              </p>
              <p className="text-slate-600">
                If the player is under 18, we'll need the parent or guardian to review and 
                accept the terms and conditions on their behalf.
              </p>
            </div>

            {/* Continue Button */}
            <button
              onClick={handleContinue}
              className="bg-green-500 hover:bg-green-600 text-white font-bold py-4 px-8 rounded-2xl transition-colors text-lg shadow-lg hover:shadow-xl transform hover:scale-105 transition-transform duration-200"
            >
              Continue
            </button>
          </div>

          {/* Right Content - Soccer Field Illustration */}
          <div className="flex justify-center items-center">
            <div className="relative">
              <Image 
                src="/images/soccer-field-illustration.png" 
                alt="Soccer Field with Live Stream" 
                width={600} 
                height={400} 
                className="object-contain"
              />
            </div>
          </div>
        </div>
      </main>
    </BackgroundLayout>
  );
}
