"use client";

import Image from "next/image";
import Link from "next/link";
import BackgroundLayout from "../components/BackgroundLayout";

export default function EmailSent() {
  return (
    <BackgroundLayout>
      {/* Header */}
      <header className="flex justify-between items-center p-8">
        <div className="flex items-center">
          <Image src="/images/koach_white.png" alt="Koach" width={140} height={60} className="mr-3" />
        </div>
        <button className="text-white text-lg font-medium hover:underline">Terms and Conditions</button>
      </header>

      {/* Main content */}
      <main className="flex justify-center items-center min-h-[calc(100vh-200px)] px-4">
        <div className="bg-white/95 backdrop-blur-sm rounded-3xl p-12 max-w-2xl w-full mx-4 shadow-2xl border border-gray-100">
          <div className="text-center mb-8">
            <h1 className="text-3xl md:text-4xl font-bold text-slate-800 mb-6"><PERSON><PERSON></h1>

            <div className="flex justify-center mb-8">
              <Image src="/images/mail-sent.png" alt="Email sent" width={150} height={150} className="object-contain" />
            </div>

            <div className="space-y-4 text-center max-w-lg mx-auto">
              <p className="text-slate-600 text-lg">
                An email with a link to the Terms and Conditions has been sent to your parent or guardian. Please ask them to check their inbox (including the
                spam folder) for the email.
              </p>

              <p className="text-slate-600 text-lg">They will need to review and accept the terms on your behalf.</p>
            </div>
          </div>

          <div className="flex justify-center">
            <Link
              href="/"
              className="bg-gradient-to-r from-emerald-500 to-emerald-600 hover:from-emerald-600 hover:to-emerald-700 text-white text-center font-bold py-2 md:py-5 px-8 md:px-16 rounded-2xl transition-all duration-300 transform hover:scale-105 hover:shadow-xl text-xl shadow-lg"
            >
              Return Home
            </Link>
          </div>
        </div>
      </main>
    </BackgroundLayout>
  );
}
